namespace SisprecPrAnalysis.Application
{
    public class PrAnalysisService : IPrAnalysisService
    {
        private readonly IGitService _gitService;
        private readonly ILlmAnalysisService _llmAnalysisService;
        private readonly IWikiDocsService _wikiDocsService;

        public PrAnalysisService(
            IGitService gitService,
            ILlmAnalysisService llmAnalysisService,
            IWikiDocsService wikiDocsService)
        {
            _gitService = gitService ?? throw new ArgumentNullException(nameof(gitService));
            _llmAnalysisService = llmAnalysisService ?? throw new ArgumentNullException(nameof(llmAnalysisService));
            _wikiDocsService = wikiDocsService ?? throw new ArgumentNullException(nameof(wikiDocsService));
        }

        public async Task<PrAnalysisResponse> AnalyzePullRequestAsync(PrAnalysisServiceRequest request)
        {
            if (string.IsNullOrWhiteSpace(request.RepositoryPath))
                return new PrAnalysisResponse { Success = false, Message = "Repository path is required." };
            if (string.IsNullOrWhiteSpace(request.SourceBranch))
                return new PrAnalysisResponse { Success = false, Message = "Source branch is required." };
            if (string.IsNullOrWhiteSpace(request.TargetBranch))
                return new PrAnalysisResponse { Success = false, Message = "Target branch is required." };

            try
            {
                var rulesContext = await _wikiDocsService.GetRulesAsync();
                var gitChangedFiles = await _gitService.GetChangedFilesAsync(request.RepositoryPath, request.TargetBranch, request.SourceBranch);

                var analysisResultData = new AnalysisResultDataDto
                {
                    SourceBranch = request.SourceBranch,
                    TargetBranch = request.TargetBranch,
                    Files = new List<FileAnalysisResultDto>()
                };

                int totalIssuesFound = 0;

                foreach (var gitChangedFile in gitChangedFiles)
                {
                    var fileAnalysis = new FileAnalysisResultDto
                    {
                        FilePath = gitChangedFile.FilePath,
                        Status = gitChangedFile.Status.ToString(), // Assumes ChangeStatus enum matches string values
                        Issues = new List<IssueDetailDto>()
                    };

                    if (gitChangedFile.Status == ChangeStatus.Deleted)
                    {
                        fileAnalysis.GeneralFeedback = "File was deleted. No analysis performed.";
                        analysisResultData.Files.Add(fileAnalysis);
                        continue;
                    }

                    var fileDiff = await _gitService.GetFileDiffAsync(request.RepositoryPath, request.TargetBranch, request.SourceBranch, gitChangedFile.FilePath);
                    fileAnalysis.DiffContent = fileDiff; // Store the diff

                    if (string.IsNullOrWhiteSpace(fileDiff))
                    {
                        fileAnalysis.GeneralFeedback = "No diff content found. Skipping LLM analysis.";
                        analysisResultData.Files.Add(fileAnalysis);
                        continue;
                    }

                    var llmRawOutput = await _llmAnalysisService.AnalyzeCodeAsync(fileDiff, rulesContext);

                    // Placeholder LLM Parsing Logic - THIS NEEDS TO BE ROBUST
                    // Assumes LLM output is line-separated: "Issue: [Desc] Severity: [Sev] Line: [L] Suggestion: [Sugg]"
                    var llmIssues = ParseLlmOutput(llmRawOutput, gitChangedFile.FilePath);
                    fileAnalysis.Issues.AddRange(llmIssues);
                    totalIssuesFound += llmIssues.Count;

                    if (!llmIssues.Any())
                    {
                        fileAnalysis.GeneralFeedback = "LLM analysis performed. No specific issues found by current parsing logic.";
                    }
                    else
                    {
                        fileAnalysis.GeneralFeedback = $"LLM analysis found {llmIssues.Count} potential issues.";
                    }
                    analysisResultData.Files.Add(fileAnalysis);
                }

                analysisResultData.Summary = $"Analysis complete for PR: {request.SourceBranch} -> {request.TargetBranch}. " +
                                             $"Processed {gitChangedFiles.Count} changed files. Found {totalIssuesFound} potential issues.";

                return new PrAnalysisResponse
                {
                    Success = true,
                    Message = "Pull request analysis complete.",
                    AnalysisResultData = analysisResultData
                };
            }
            catch (Exception ex)
            {
                // Log the exception (ex) properly in a real application
                return new PrAnalysisResponse
                {
                    Success = false,
                    Message = $"An error occurred during analysis: {ex.Message}",
                    AnalysisResultData = new AnalysisResultDataDto
                    {
                        Summary = $"Error: {ex.Message}",
                        Files = new List<FileAnalysisResultDto>(),
                        SourceBranch = request.SourceBranch,
                        TargetBranch = request.TargetBranch
                    }
                };
            }
        }

        // Placeholder LLM Output Parsing - THIS IS CRITICAL AND NEEDS ROBUST IMPLEMENTATION
        private List<IssueDetailDto> ParseLlmOutput(string llmOutput, string filePath)
        {
            var issues = new List<IssueDetailDto>();
            if (string.IsNullOrWhiteSpace(llmOutput)) return issues;

            // Example: "Issue: Use const. Severity: Warning. Line: 5. Suggestion: Change let to const."
            // This is a very naive parser. A real LLM might need JSON output or more complex regex.
            var lines = llmOutput.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var line in lines)
            {
                // Extremely basic parsing - assumes a simple, predictable format.
                if (line.ToLower().Contains("no issues") || line.ToLower().Contains("looks good")) continue;

                var issue = new IssueDetailDto { Description = line, Severity = "Information", FilePath = filePath }; // Default

                // Try to extract a bit more detail with Regex (very basic)
                var sevMatch = System.Text.RegularExpressions.Regex.Match(line, @"Severity:\s*(\w+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                if (sevMatch.Success) issue.Severity = sevMatch.Groups[1].Value;

                var lineMatch = System.Text.RegularExpressions.Regex.Match(line, @"Line:\s*(\d+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                if (lineMatch.Success && int.TryParse(lineMatch.Groups[1].Value, out int lineNum)) issue.LineStart = lineNum;

                var descMatch = System.Text.RegularExpressions.Regex.Match(line, @"Issue:\s*([^.]+)");
                if (descMatch.Success) issue.Description = descMatch.Groups[1].Value.Trim();
                else issue.Description = line; // fallback to full line if no "Issue:" prefix

                var suggMatch = System.Text.RegularExpressions.Regex.Match(line, @"Suggestion:\s*([^.]+)");
                if (suggMatch.Success) issue.Suggestion = suggMatch.Groups[1].Value.Trim();

                issues.Add(issue);
            }
            return issues;
        }
    }
}

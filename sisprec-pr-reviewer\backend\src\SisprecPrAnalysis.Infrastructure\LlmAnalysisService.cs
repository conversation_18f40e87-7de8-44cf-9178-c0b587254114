using Microsoft.Extensions.Options;
using SisprecPrAnalysis.Application;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace SisprecPrAnalysis.Infrastructure
{
    public class LlmAnalysisService : ILlmAnalysisService
    {
        private readonly HttpClient _httpClient;
        private readonly LlmServiceOptions _options;

        public LlmAnalysisService(HttpClient httpClient, IOptions<LlmServiceOptions> options)
        {
            _httpClient = httpClient;
            _options = options.Value;

            if (string.IsNullOrWhiteSpace(_options.ApiKey))
            {
                throw new ArgumentException("LLM API Key is missing. Please configure it in appsettings.json or user secrets.");
            }
            if (string.IsNullOrWhiteSpace(_options.Endpoint))
            {
                throw new ArgumentException("LLM Endpoint is missing. Please configure it in appsettings.json or user secrets.");
            }
            if (string.IsNullOrWhiteSpace(_options.Model))
            {
                throw new ArgumentException("LLM Model is missing. Please configure it in appsettings.json or user secrets.");
            }

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _options.ApiKey);
            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }

        public async Task<string> AnalyzeCodeAsync(string codeSnippet, string context = null)
        {
            if (string.IsNullOrWhiteSpace(codeSnippet))
            {
                return string.Empty; // Or throw an ArgumentNullException
            }

            var prompt = $"Please analyze the following code snippet for potential issues, rule violations, and suggestions for improvement.";
            if (!string.IsNullOrWhiteSpace(context))
            {
                prompt += $"\n\nConsider the following context or documentation:\n{context}";
            }
            prompt += $"\n\nCode to analyze:\n```\n{codeSnippet}\n```";

            var requestBody = new
            {
                model = _options.Model,
                messages = new[]
                {
                    new { role = "system", content = "You are an expert code reviewer." },
                    new { role = "user", content = prompt }
                },
                temperature = _options.Temperature
            };

            var content = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json");

            try
            {
                var response = await _httpClient.PostAsync(_options.Endpoint, content);
                response.EnsureSuccessStatusCode(); // Throws HttpRequestException for bad status codes

                var responseBody = await response.Content.ReadAsStringAsync();
                // Basic parsing for now, assuming OpenAI-like response structure
                // This will need to be more robust based on the actual LLM response
                using (var jsonDoc = JsonDocument.Parse(responseBody))
                {
                    if (jsonDoc.RootElement.TryGetProperty("choices", out var choicesElement) && choicesElement.GetArrayLength() > 0)
                    {
                        if (choicesElement[0].TryGetProperty("message", out var messageElement) && messageElement.TryGetProperty("content", out var contentElement))
                        {
                            return contentElement.GetString();
                        }
                    }
                    // Fallback or more specific error handling if the expected structure isn't found
                    return $"Could not parse LLM response. Raw response: {responseBody}";
                }
            }
            catch (HttpRequestException e)
            {
                // Log the error, handle specific status codes (401, 429, etc.)
                // For now, rethrow or return a generic error message
                return $"Error calling LLM API: {e.Message}. Status Code: {e.StatusCode}";
            }
            catch (Exception e)
            {
                // General error handling
                return $"An unexpected error occurred while analyzing code: {e.Message}";
            }
        }
    }
}

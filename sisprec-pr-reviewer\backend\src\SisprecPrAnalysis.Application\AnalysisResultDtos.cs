namespace SisprecPrAnalysis.Application
{
    // Corresponds to AnalysisResultData in TypeScript
    public class AnalysisResultDataDto
    {
        public string Summary { get; set; }
        public List<FileAnalysisResultDto> Files { get; set; }
        public string SourceBranch { get; set; }
        public string TargetBranch { get; set; }

        public AnalysisResultDataDto()
        {
            Files = new List<FileAnalysisResultDto>();
        }
    }

    // Corresponds to FileAnalysisResult in TypeScript
    public class FileAnalysisResultDto
    {
        public string FilePath { get; set; }
        public string Status { get; set; } // "Added", "Modified", "Deleted", "Renamed", "Unknown"
        public List<IssueDetailDto> Issues { get; set; }
        public string GeneralFeedback { get; set; }
        public string DiffContent { get; set; } // Optional raw diff

        public FileAnalysisResultDto()
        {
            Issues = new List<IssueDetailDto>();
        }
    }

    // Corresponds to IssueDetail in TypeScript
    public class IssueDetailDto
    {
        public string RuleId { get; set; }
        public string Description { get; set; }
        public string Suggestion { get; set; }
        public string Severity { get; set; } // "Error", "Warning", "Information", "Hint"
        public int? LineStart { get; set; }
        public int? LineEnd { get; set; }
        public int? ColumnStart { get; set; }
        public int? ColumnEnd { get; set; }
        public string CodeSnippet { get; set; }
        public string FilePath { get; internal set; }
    }
}

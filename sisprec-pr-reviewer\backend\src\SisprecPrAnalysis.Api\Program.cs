using SisprecPrAnalysis.Application;
using SisprecPrAnalysis.Infrastructure;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Configure options
builder.Services.Configure<LlmServiceOptions>(builder.Configuration.GetSection(LlmServiceOptions.SectionName));
builder.Services.Configure<WikiDocsServiceOptions>(builder.Configuration.GetSection(WikiDocsServiceOptions.SectionName)); // Corrected: WikiDocsServiceOptions.SectionName

// Register custom services
builder.Services.AddHttpClient<ILlmAnalysisService, LlmAnalysisService>(); // HttpClient for LlmAnalysisService

builder.Services.AddScoped<IGitService, GitService>();
builder.Services.AddScoped<IWikiDocsService, WikiDocsService>();
builder.Services.AddScoped<ILlmAnalysisService, LlmAnalysisService>(); // Re-registered ILlmAnalysisService
builder.Services.AddScoped<IPrAnalysisService, PrAnalysisService>();


var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseAuthorization();
app.MapControllers();
app.Run();
